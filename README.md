# KelvinKMS.com - 完整系統使用說明

## 系統概述

KelvinKMS.com 是一個多功能的網站系統，提供以下主要功能：
- 用戶註冊和登錄系統
- KMS Credit 錢包系統
- 照片/視頻優化服務
- 打印服務
- PC 組裝服務
- 推薦系統 (Affiliate System)
- 即時聊天系統
- 管理員後台管理

## 系統架構

### 目錄結構
```
KelvinKMS.com/
├── index.php                          # 主頁面
├── KMS_Apps/                          # 主要應用程序目錄
│   ├── KMS_Core/                      # 核心功能
│   │   ├── KMS_Config/                # 配置文件
│   │   ├── KMS_Functions/             # 通用函數
│   │   ├── KMS_Language/              # 多語言支持
│   │   └── KMS_Security/              # 安全功能
│   ├── KMS_Index/                     # 首頁相關功能
│   │   ├── KMS_Authentication/        # 登錄註冊
│   │   ├── KMS_Homepage/              # 主頁內容
│   │   ├── KMS_LiveChat/              # 即時聊天
│   │   └── KMS_UserTracking/          # 用戶追蹤
│   ├── KMS_Member/                    # 會員功能
│   │   ├── KMS_member.php             # 會員主頁面
│   │   ├── KMS_CSS/                   # 會員頁面樣式
│   │   ├── KMS_JS/                    # 會員頁面腳本
│   │   ├── KMS_Modals/                # 模態框
│   │   ├── KMS_Affiliate/             # 推薦系統
│   │   ├── KMS_Dashboard/             # 會員儀表板
│   │   ├── KMS_Orders/                # 訂單管理
│   │   ├── KMS_PCBuilder/             # PC 組裝工具
│   │   ├── KMS_Payment/               # 支付處理
│   │   └── KMS_Wallet/                # 錢包系統
│   └── KMS_Admin/                     # 管理員功能
│       ├── KMS_Dashboard/             # 管理員儀表板
│       ├── KMS_MemberManagement/      # 會員管理
│       ├── KMS_OrderManagement/       # 訂單管理
│       ├── KMS_PCManagement/          # PC 組件管理
│       ├── KMS_PriceManagement/       # 價格管理
│       ├── KMS_CreditManagement/      # 信用管理
│       ├── KMS_ChatManagement/        # 聊天管理
│       └── KMS_AffiliateManagement/   # 推薦管理
├── SQL/                               # 數據庫文件
│   ├── complete_database_setup.sql    # 完整數據庫設置
│   └── insert_default_users.sql       # 默認用戶插入
├── Favicon/                           # 網站圖標
├── KMS_VIP_PC/                        # VIP PC 圖片
└── lang/                              # 語言文件
```

## 數據庫結構

### 主要數據表

#### 1. users - 用戶表
存儲所有用戶的基本信息和認證數據。

**字段說明：**
- `id`: 用戶唯一標識符
- `username`: 用戶名（唯一）
- `password`: 加密後的密碼
- `email`: 電子郵件地址（唯一）
- `first_name`, `last_name`: 姓名
- `nickname`: 暱稱
- `gender`: 性別 (male/female/other/prefer_not_to_say)
- `birthday`: 生日
- `language`: 語言偏好 (en/zh-CN)
- `phone_number`: 電話號碼
- `street_address`, `city`, `state`, `zip_code`: 地址信息
- `is_verified`: 是否已驗證
- `is_active`: 是否活躍
- `is_admin`: 是否為管理員
- `created_at`, `updated_at`: 創建和更新時間
- `last_login`, `last_seen`: 最後登錄和在線時間

#### 2. user_wallets - 用戶錢包表
管理每個用戶的 KMS Credit 餘額。

**字段說明：**
- `user_id`: 關聯用戶ID
- `balance`: 可用餘額
- `frozen_balance`: 凍結餘額（待處理交易）
- `commission_balance`: 推薦佣金餘額
- `total_deposited`: 總充值金額
- `total_spent`: 總消費金額
- `total_commissions`: 總佣金收入

#### 3. credit_transactions - 信用交易記錄表
記錄所有 KMS Credit 相關的交易。

**交易類型：**
- `deposit`: 充值
- `withdraw`: 提現
- `spend`: 消費
- `refund`: 退款
- `admin_gift`: 管理員贈送
- `admin_deduct`: 管理員扣除
- `affiliate`: 推薦佣金

#### 4. orders - 服務訂單表
存儲照片/視頻優化和打印服務的訂單。

#### 5. pc_orders - PC 組裝訂單表
存儲 PC 組裝相關的訂單。

#### 6. affiliate_codes - 推薦代碼表
管理用戶的推薦代碼。

#### 7. affiliate_referrals - 推薦記錄表
記錄推薦關係。

#### 8. affiliate_commissions - 推薦佣金表
管理推薦佣金的發放。

#### 9. service_prices - 服務價格表
存儲所有服務的動態定價。

**服務類別：**
- `optimize`: 照片/視頻優化服務
- `print`: 打印服務

#### 10. pc_components - PC 組件表
存儲 PC 組件信息和價格。

#### 11. pc_prebuilt_configs - 預設 PC 配置表
存儲預設的 PC 配置方案。

#### 12. chat_sessions & chat_messages - 聊天系統表
管理即時聊天功能。

## 安裝和配置

### 1. 數據庫設置

1. 創建 MySQL 數據庫：
```sql
CREATE DATABASE kelvinkms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 執行完整數據庫設置：
```bash
mysql -u username -p kelvinkms < SQL/complete_database_setup.sql
```

3. 插入默認用戶：
```bash
mysql -u username -p kelvinkms < SQL/insert_default_users.sql
```

### 2. 配置文件設置

編輯 `KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php`：

```php
// 數據庫配置
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'your_username');
define('DB_PASSWORD', 'your_password');
define('DB_NAME', 'kelvinkms');

// 其他配置...
```

### 3. 文件權限設置

確保以下目錄具有寫入權限：
- `logs/`
- `KMS_Apps/KMS_Member/KMS_Affiliate/logs/`

## 默認用戶賬戶

系統預設了兩個用戶賬戶：

### 管理員賬戶
- **用戶名**: KMSAdmin
- **密碼**: k1e9l9v9in
- **權限**: 完整管理員權限
- **初始餘額**: $1000.00

### 測試會員賬戶
- **用戶名**: KelvinKMS
- **密碼**: k1e9l9v9in
- **權限**: 普通會員
- **初始餘額**: $100.00

## 主要功能說明

### 1. KMS Credit 系統

KMS Credit 是網站的虛擬貨幣系統，用戶可以：
- 通過多種支付方式充值
- 用於購買各種服務
- 提現到外部賬戶
- 通過推薦獲得佣金

**支持的支付方式：**
- PayPal
- Stripe (信用卡/借記卡)
- Square
- Venmo
- Zelle

### 2. 服務系統

#### 照片/視頻優化服務
- 照片優化：$3.00/張
- 照片去水印：$3.00/張
- 視頻優化：$2.00/分鐘
- 視頻去水印（簡單）：$10.00/30分鐘內
- 視頻去水印（困難）：$30.00/30分鐘內

#### 打印服務
- 多種紙張類型和規格
- 黑白和彩色打印選項
- 照片紙打印
- 裝裱服務
- 相冊製作

### 3. PC 組裝服務

提供三種模式：
- **簡單模式**: 基於用途和預算的快速選擇
- **詳細模式**: 自定義選擇每個組件
- **預設配置**: 專業推薦的完整配置

**支持的組件類別：**
- CPU (Intel 285K, 265K, AMD 7800X3D, 9800X3D, 9950X3D)
- 內存 (DDR5 32-128GB)
- 存儲 (NVMe SSD 2-8TB)
- 顯卡 (RTX 5070-5090)
- 電源 (850-1200W)
- 機箱（小/中/大型）
- Windows 11 (Home/Pro)

### 4. 推薦系統

- 每個用戶都有唯一的推薦代碼
- 成功推薦可獲得 $50 佣金
- 佣金需要管理員審核後發放
- 佣金可轉換為 KMS Credit 或提現

### 5. 管理員功能

管理員可以：
- 管理所有用戶賬戶
- 添加/編輯/刪除會員
- 調整用戶 KMS Credit 餘額
- 管理服務價格
- 處理訂單
- 管理 PC 組件和價格
- 審核推薦佣金
- 查看系統統計

## 技術特性

### 安全特性
- 密碼哈希加密
- SQL 注入防護
- XSS 防護
- CSRF 保護
- 會話管理
- 輸入驗證

### 響應式設計
- 支持桌面和移動設備
- 自適應佈局
- 觸摸友好的界面

### 多語言支持
- 英語 (en)
- 簡體中文 (zh-CN)
- 易於擴展其他語言

### 性能優化
- 數據庫索引優化
- 靜態資源分離
- 異步 JavaScript 加載
- 緩存機制

## 維護和故障排除

### 常見問題

1. **數據庫連接失敗**
   - 檢查數據庫配置
   - 確認數據庫服務運行
   - 驗證用戶權限

2. **文件上傳失敗**
   - 檢查文件權限
   - 確認上傳目錄存在
   - 檢查 PHP 上傳限制

3. **支付處理問題**
   - 驗證支付 API 配置
   - 檢查網絡連接
   - 查看錯誤日誌

### 日誌文件位置
- 系統日誌：`logs/`
- 推薦系統日誌：`KMS_Apps/KMS_Member/KMS_Affiliate/logs/`
- PHP 錯誤日誌：服務器配置位置

### 備份建議
- 定期備份數據庫
- 備份上傳的文件
- 保存配置文件副本

## 更新和升級

### 版本控制
- 使用 Git 進行版本控制
- 標記重要版本
- 維護更新日誌

### 升級步驟
1. 備份當前系統
2. 測試新版本
3. 更新數據庫結構
4. 部署新代碼
5. 驗證功能正常

## 支持和聯繫

如需技術支持或有任何問題，請通過以下方式聯繫：
- 網站即時聊天系統
- 電子郵件：<EMAIL>
- 系統管理員：KMSAdmin

---

**最後更新**: 2025-07-15
**版本**: 1.0.0
**作者**: KelvinKMS 開發團隊
