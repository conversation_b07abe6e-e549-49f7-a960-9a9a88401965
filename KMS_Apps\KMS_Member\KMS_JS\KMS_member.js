// KMS Member Dashboard JavaScript

// Global variables
let currentMode = 'simple';
let selectedComponents = {};
let serviceOrders = {};
let walletData = {};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeMemberDashboard();
});

// Main initialization function
function initializeMemberDashboard() {
    loadWalletData();
    loadServicePrices();
    loadPCComponents();
    loadAffiliateData();
    setupEventListeners();
    startHeartbeat();
}

// Setup event listeners
function setupEventListeners() {
    // Wallet action buttons
    const depositBtn = document.getElementById('deposit-btn');
    const withdrawBtn = document.getElementById('withdraw-btn');
    const transferBtn = document.getElementById('transfer-btn');
    
    if (depositBtn) depositBtn.addEventListener('click', showDepositModal);
    if (withdrawBtn) withdrawBtn.addEventListener('click', showWithdrawModal);
    if (transferBtn) transferBtn.addEventListener('click', showTransferModal);
    
    // PC Builder mode buttons
    const modeButtons = document.querySelectorAll('.mode-btn');
    modeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            switchPCBuilderMode(this.dataset.mode);
        });
    });
    
    // Service order buttons
    const orderButtons = document.querySelectorAll('.create-order-btn');
    orderButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            createServiceOrder(this.dataset.category);
        });
    });
    
    // Modal close buttons
    const closeButtons = document.querySelectorAll('.close');
    closeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            closeModal(this.closest('.modal'));
        });
    });
}

// Wallet Functions
async function loadWalletData() {
    try {
        const response = await fetch('KMS_Wallet/KMS_PHP/KMS_credit_wallet.php?action=get_balance', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const data = await response.json();
        if (data.success) {
            walletData = data.data;
            updateWalletDisplay();
        } else {
            console.error('Failed to load wallet data:', data.message);
        }
    } catch (error) {
        console.error('Error loading wallet data:', error);
    }
}

function updateWalletDisplay() {
    const balanceElement = document.getElementById('walletBalance');
    const totalDepositedElement = document.getElementById('totalDeposited');
    const totalSpentElement = document.getElementById('totalSpent');
    const frozenBalanceElement = document.getElementById('frozenBalance');
    
    if (balanceElement) balanceElement.textContent = `$${parseFloat(walletData.balance || 0).toFixed(2)}`;
    if (totalDepositedElement) totalDepositedElement.textContent = `$${parseFloat(walletData.total_deposited || 0).toFixed(2)}`;
    if (totalSpentElement) totalSpentElement.textContent = `$${parseFloat(walletData.total_spent || 0).toFixed(2)}`;
    if (frozenBalanceElement) frozenBalanceElement.textContent = `$${parseFloat(walletData.frozen_balance || 0).toFixed(2)}`;
}

// Service Functions
async function loadServicePrices() {
    try {
        const response = await fetch('KMS_Orders/KMS_PHP/KMS_service_prices_api.php?action=get_prices', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const data = await response.json();
        if (data.success) {
            displayServicePrices(data.data);
        } else {
            console.error('Failed to load service prices:', data.message);
        }
    } catch (error) {
        console.error('Error loading service prices:', error);
    }
}

function displayServicePrices(prices) {
    const optimizeContainer = document.getElementById('optimize-services');
    const printContainer = document.getElementById('print-services');
    
    if (optimizeContainer) {
        const optimizeServices = prices.filter(p => p.service_category === 'optimize');
        optimizeContainer.innerHTML = generateServiceHTML(optimizeServices, 'optimize');
    }
    
    if (printContainer) {
        const printServices = prices.filter(p => p.service_category === 'print');
        printContainer.innerHTML = generateServiceHTML(printServices, 'print');
    }
}

function generateServiceHTML(services, category) {
    return services.map(service => `
        <div class="service-item" data-service-id="${service.id}">
            <div class="service-info">
                <div class="service-name">${service.item_name}</div>
                <div class="service-description">${service.description}</div>
                <div class="service-price">$${parseFloat(service.base_price).toFixed(2)} / ${service.unit}</div>
            </div>
            <div class="quantity-controls">
                <button class="quantity-btn" onclick="changeQuantity('${service.id}', -1)">-</button>
                <input type="number" class="quantity-input" id="qty-${service.id}" value="0" min="0" onchange="updateServiceOrder('${service.id}', this.value)">
                <button class="quantity-btn" onclick="changeQuantity('${service.id}', 1)">+</button>
            </div>
        </div>
    `).join('');
}

function changeQuantity(serviceId, change) {
    const input = document.getElementById(`qty-${serviceId}`);
    const currentValue = parseInt(input.value) || 0;
    const newValue = Math.max(0, currentValue + change);
    input.value = newValue;
    updateServiceOrder(serviceId, newValue);
}

function updateServiceOrder(serviceId, quantity) {
    if (quantity > 0) {
        serviceOrders[serviceId] = parseInt(quantity);
    } else {
        delete serviceOrders[serviceId];
    }
    updateOrderSummary();
}

function updateOrderSummary() {
    // Implementation for updating order summary
    console.log('Current service orders:', serviceOrders);
}

// PC Builder Functions
async function loadPCComponents() {
    try {
        const response = await fetch('KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php?action=get_components', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const data = await response.json();
        if (data.success) {
            displayPCComponents(data.data);
        } else {
            console.error('Failed to load PC components:', data.message);
        }
    } catch (error) {
        console.error('Error loading PC components:', error);
    }
}

function displayPCComponents(components) {
    // Implementation for displaying PC components
    console.log('PC Components loaded:', components);
}

function switchPCBuilderMode(mode) {
    currentMode = mode;
    
    // Update active button
    document.querySelectorAll('.mode-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-mode="${mode}"]`).classList.add('active');
    
    // Show/hide appropriate sections
    const sections = document.querySelectorAll('.pc-mode-section');
    sections.forEach(section => {
        section.style.display = section.dataset.mode === mode ? 'block' : 'none';
    });
}

// Affiliate Functions
async function loadAffiliateData() {
    try {
        const response = await fetch('KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_data', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const data = await response.json();
        if (data.success) {
            displayAffiliateData(data.data);
        } else {
            console.error('Failed to load affiliate data:', data.message);
        }
    } catch (error) {
        console.error('Error loading affiliate data:', error);
    }
}

function displayAffiliateData(data) {
    const codeElement = document.getElementById('affiliate-code');
    const statsContainer = document.getElementById('affiliate-stats');
    
    if (codeElement && data.affiliate_code) {
        codeElement.textContent = data.affiliate_code;
    }
    
    if (statsContainer && data.stats) {
        updateAffiliateStats(data.stats);
    }
}

function updateAffiliateStats(stats) {
    const elements = {
        'total-referrals': stats.total_referrals || 0,
        'confirmed-referrals': stats.confirmed_referrals || 0,
        'pending-referrals': stats.pending_referrals || 0,
        'total-commissions': `$${parseFloat(stats.total_commissions || 0).toFixed(2)}`
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) element.textContent = value;
    });
}

// Modal Functions
function showDepositModal() {
    const modal = document.getElementById('deposit-modal');
    if (modal) modal.style.display = 'block';
}

function showWithdrawModal() {
    const modal = document.getElementById('withdraw-modal');
    if (modal) modal.style.display = 'block';
}

function showTransferModal() {
    const modal = document.getElementById('transfer-modal');
    if (modal) modal.style.display = 'block';
}

function closeModal(modal) {
    if (modal) modal.style.display = 'none';
}

// Order Functions
async function createServiceOrder(category) {
    const orderItems = Object.entries(serviceOrders).map(([serviceId, quantity]) => ({
        service_id: serviceId,
        quantity: quantity
    }));
    
    if (orderItems.length === 0) {
        alert('Please select at least one service item.');
        return;
    }
    
    const useCredit = document.getElementById('use-credit')?.checked || false;
    const notes = document.getElementById('order-notes')?.value || '';
    
    try {
        const formData = new FormData();
        formData.append('action', 'create_order');
        formData.append('service_category', category);
        formData.append('order_items', JSON.stringify(orderItems));
        formData.append('use_credit', useCredit ? '1' : '0');
        formData.append('notes', notes);
        
        const response = await fetch('KMS_Orders/KMS_PHP/KMS_service_orders_api.php', {
            method: 'POST',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        });
        
        const data = await response.json();
        if (data.success) {
            alert('Order created successfully!');
            // Reset form
            serviceOrders = {};
            document.querySelectorAll('.quantity-input').forEach(input => input.value = '0');
            updateOrderSummary();
            loadWalletData(); // Refresh wallet data
        } else {
            alert('Failed to create order: ' + data.message);
        }
    } catch (error) {
        console.error('Error creating order:', error);
        alert('An error occurred while creating the order.');
    }
}

// Heartbeat function to keep user online
function startHeartbeat() {
    // Send heartbeat every minute
    setInterval(sendHeartbeat, 60000);
}

async function sendHeartbeat() {
    try {
        await fetch('../KMS_Index/KMS_UserTracking/KMS_PHP/KMS_heartbeat.php', {
            method: 'POST',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
    } catch (error) {
        console.error('Heartbeat failed:', error);
    }
}

// Utility Functions
function formatCurrency(amount) {
    return `$${parseFloat(amount || 0).toFixed(2)}`;
}

function showNotification(message, type = 'info') {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background: ${type === 'error' ? '#f44336' : type === 'success' ? '#4CAF50' : '#2196F3'};
        color: white;
        border-radius: 5px;
        z-index: 10000;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Copied to clipboard!', 'success');
    }).catch(err => {
        console.error('Failed to copy:', err);
        showNotification('Failed to copy to clipboard', 'error');
    });
}

// Page unload handler for offline tracking
window.addEventListener('beforeunload', function() {
    navigator.sendBeacon('../KMS_Index/KMS_UserTracking/KMS_PHP/KMS_log_offline.php');
});
