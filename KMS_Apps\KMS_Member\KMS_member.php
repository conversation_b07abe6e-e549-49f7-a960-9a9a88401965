<?php
/**
 * KMS Member Dashboard - Streamlined Version
 * Main member dashboard with all functionality
 */

// Error reporting and session management
ini_set('display_errors', 1);
error_reporting(E_ALL);
session_start();

// Include required files
$base_path = dirname(dirname(__DIR__));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Language' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_language.php';

// Authentication check
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../../index.php');
    exit;
}

// Store session ID for JavaScript
$session_id = session_id();
$username = htmlspecialchars($_SESSION["username"]);
?>
<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= t('member_page_title') ?></title>
    
    <!-- External CSS -->
    <link rel="stylesheet" href="KMS_CSS/KMS_member.css">
    <link rel="stylesheet" href="../KMS_Index/KMS_Authentication/KMS_CSS/KMS_custom-modal.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../../Favicon/favicon.ico">
    <link rel="icon" type="image/png" sizes="16x16" href="../../Favicon/KMS_Logo_16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="../../Favicon/KMS_Logo_32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="../../Favicon/KMS_Logo_96.png">
    <link rel="icon" type="image/png" sizes="152x152" href="../../Favicon/KMS_Logo_152.png">
    <link rel="icon" type="image/png" sizes="512x512" href="../../Favicon/KMS_Logo_512.png">
    
    <script>const SESSION_ID = '<?= $session_id ?>';</script>
</head>
<body>
    <div class="container">
        <!-- Welcome Message -->
        <h1 id="welcome-msg"><?= str_replace('{username}', $username, t('welcome_message')) ?></h1>

        <!-- KMS Credit Wallet Section -->
        <div class="wallet-section">
            <h2>💰 KMS Credit Wallet</h2>
            <div class="wallet-card">
                <div class="wallet-balance">
                    <h3>Available Balance</h3>
                    <div class="balance-amount" id="walletBalance">$0.00</div>
                </div>

                <div class="wallet-stats">
                    <div class="stat-item">
                        <h4>Total Deposited</h4>
                        <div class="stat-value" id="totalDeposited">$0.00</div>
                    </div>
                    <div class="stat-item">
                        <h4>Total Spent</h4>
                        <div class="stat-value" id="totalSpent">$0.00</div>
                    </div>
                    <div class="stat-item">
                        <h4>Frozen Balance</h4>
                        <div class="stat-value" id="frozenBalance">$0.00</div>
                    </div>
                </div>

                <div class="wallet-actions">
                    <button id="deposit-btn" class="btn-deposit">💳 Deposit</button>
                    <button id="withdraw-btn" class="btn-withdraw">💰 Withdraw</button>
                    <button id="transfer-btn" class="btn-transfer">🔄 Transfer</button>
                </div>
            </div>
        </div>

        <!-- Service Orders Section -->
        <div class="service-section">
            <h2>🛠️ Service Orders</h2>
            
            <!-- Optimize Photo & Video Services -->
            <div class="service-card">
                <h3>📸 Optimize Photo & Video</h3>
                <div id="optimize-services" class="service-grid">
                    <!-- Services will be loaded via JavaScript -->
                </div>
                <div class="order-summary">
                    <div class="order-total" id="optimize-total">Total: $0.00</div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="use-credit-optimize"> Use KMS Credit
                        </label>
                    </div>
                    <div class="form-group">
                        <label for="optimize-notes">Order Notes:</label>
                        <textarea id="optimize-notes" placeholder="Any special instructions..."></textarea>
                    </div>
                    <button class="btn-primary create-order-btn" data-category="optimize">Create Order</button>
                </div>
            </div>

            <!-- Print Services -->
            <div class="service-card">
                <h3>🖨️ Print Services</h3>
                <div id="print-services" class="service-grid">
                    <!-- Services will be loaded via JavaScript -->
                </div>
                <div class="order-summary">
                    <div class="order-total" id="print-total">Total: $0.00</div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="use-credit-print"> Use KMS Credit
                        </label>
                    </div>
                    <div class="form-group">
                        <label for="print-notes">Order Notes:</label>
                        <textarea id="print-notes" placeholder="Any special instructions..."></textarea>
                    </div>
                    <button class="btn-primary create-order-btn" data-category="print">Create Order</button>
                </div>
            </div>
        </div>

        <!-- PC Builder Section -->
        <div class="pc-builder-section">
            <h2>🖥️ PC Builder</h2>
            
            <div class="mode-selector">
                <button class="mode-btn active" data-mode="simple">Simple Mode</button>
                <button class="mode-btn" data-mode="detailed">Detailed Mode</button>
                <button class="mode-btn" data-mode="prebuilt">Pre-built PCs</button>
            </div>

            <!-- Simple Mode -->
            <div class="pc-mode-section" data-mode="simple">
                <div class="pc-component-grid" id="simple-mode-options">
                    <!-- Simple mode options will be loaded via JavaScript -->
                </div>
            </div>

            <!-- Detailed Mode -->
            <div class="pc-mode-section" data-mode="detailed" style="display: none;">
                <div class="pc-component-grid" id="detailed-mode-components">
                    <!-- Detailed components will be loaded via JavaScript -->
                </div>
            </div>

            <!-- Pre-built PCs -->
            <div class="pc-mode-section" data-mode="prebuilt" style="display: none;">
                <div class="pc-component-grid" id="prebuilt-configs">
                    <!-- Pre-built configurations will be loaded via JavaScript -->
                </div>
            </div>

            <div class="order-summary">
                <div class="order-total" id="pc-total">Total: $0.00</div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="use-credit-pc"> Use KMS Credit
                    </label>
                </div>
                <div class="form-group">
                    <label for="pc-notes">PC Build Notes:</label>
                    <textarea id="pc-notes" placeholder="Any special requirements..."></textarea>
                </div>
                <button class="btn-primary" id="create-pc-order">Build PC</button>
            </div>
        </div>

        <!-- Affiliate System Section -->
        <div class="affiliate-section">
            <h2>🤝 Affiliate System</h2>
            
            <div class="affiliate-code-display">
                <h3>Your Affiliate Code</h3>
                <div class="affiliate-code" id="affiliate-code">Loading...</div>
                <button class="btn-primary" onclick="copyToClipboard(document.getElementById('affiliate-code').textContent)">
                    📋 Copy Code
                </button>
            </div>

            <div class="referral-stats" id="affiliate-stats">
                <div class="referral-stat-card">
                    <h4>Total Referrals</h4>
                    <div class="stat-value" id="total-referrals">0</div>
                </div>
                <div class="referral-stat-card">
                    <h4>Confirmed Referrals</h4>
                    <div class="stat-value" id="confirmed-referrals">0</div>
                </div>
                <div class="referral-stat-card">
                    <h4>Pending Referrals</h4>
                    <div class="stat-value" id="pending-referrals">0</div>
                </div>
                <div class="referral-stat-card">
                    <h4>Total Commissions</h4>
                    <div class="stat-value" id="total-commissions">$0.00</div>
                </div>
            </div>
        </div>

        <!-- Account Settings Section -->
        <div class="service-section">
            <h2>⚙️ Account Settings</h2>
            <div class="service-card">
                <p>For account changes, please contact us via Live Chat.</p>
                <button class="btn-primary" onclick="window.open('../../index.php#live-chat', '_blank')">
                    💬 Live Chat Support
                </button>
            </div>
        </div>

        <!-- Logout Section -->
        <div class="service-section">
            <div class="service-card" style="text-align: center;">
                <button class="btn-danger" onclick="window.location.href='../KMS_Index/KMS_Authentication/KMS_PHP/KMS_logout.php'">
                    🚪 Logout
                </button>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <?php include 'KMS_Modals/KMS_wallet_modals.php'; ?>
    <?php include 'KMS_Modals/KMS_order_modals.php'; ?>

    <!-- External JavaScript -->
    <script src="KMS_JS/KMS_member.js"></script>
</body>
</html>
