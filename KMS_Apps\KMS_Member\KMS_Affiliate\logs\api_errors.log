[14-Jul-2025 06:08:08 Europe/Berlin] 
================================================================================
[14-Jul-2025 06:08:08 Europe/Berlin] [2025-07-14 06:08:08] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_referral_history&PHPSESSID=fa0nbs29b4gi0o2qga5f2qr4cv
[14-Jul-2025 06:08:08 Europe/Berlin] Request method: GET
[14-Jul-2025 06:08:08 Europe/Berlin] Session ID: none
[14-Jul-2025 06:08:08 Europe/Berlin] API Error: Array
(
    [message] => Table 'kelvinkms.affiliate_commissions' doesn't exist
    [file] => D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php
    [line] => 50
    [trace] => #0 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php(50): mysqli_prepare(Object(mysqli), 'SELECT ar.*, u....')
#1 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_system.php(364): execute_query(Object(mysqli), 'SELECT ar.*, u....', 'iii', Array)
#2 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(238): AffiliateSystem->getReferralHistory(2, 20, 0)
#3 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(109): getReferralHistory(Object(AffiliateSystem), 2)
#4 {main}
    [session] => Array
        (
            [loggedin] => 1
            [id] => 2
            [username] => KelvinKMS
            [is_admin] => 
            [test] => working
            [language] => en
        )

)

[14-Jul-2025 06:08:31 Europe/Berlin] 
================================================================================
[14-Jul-2025 06:08:31 Europe/Berlin] [2025-07-14 06:08:31] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_referral_history&PHPSESSID=fa0nbs29b4gi0o2qga5f2qr4cv
[14-Jul-2025 06:08:31 Europe/Berlin] Request method: GET
[14-Jul-2025 06:08:31 Europe/Berlin] Session ID: none
[14-Jul-2025 06:08:31 Europe/Berlin] API Error: Array
(
    [message] => Table 'kelvinkms.affiliate_commissions' doesn't exist
    [file] => D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php
    [line] => 50
    [trace] => #0 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php(50): mysqli_prepare(Object(mysqli), 'SELECT ar.*, u....')
#1 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_system.php(364): execute_query(Object(mysqli), 'SELECT ar.*, u....', 'iii', Array)
#2 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(238): AffiliateSystem->getReferralHistory(2, 20, 0)
#3 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(109): getReferralHistory(Object(AffiliateSystem), 2)
#4 {main}
    [session] => Array
        (
            [loggedin] => 1
            [id] => 2
            [username] => KelvinKMS
            [is_admin] => 
            [test] => working
            [language] => en
        )

)

[14-Jul-2025 06:13:22 Europe/Berlin] 
================================================================================
[14-Jul-2025 06:13:22 Europe/Berlin] [2025-07-14 06:13:22] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info&t=1752466402096
[14-Jul-2025 06:13:22 Europe/Berlin] Request method: GET
[14-Jul-2025 06:13:22 Europe/Berlin] Session ID: none
[14-Jul-2025 10:23:17 Europe/Berlin] 
================================================================================
[14-Jul-2025 10:23:17 Europe/Berlin] [2025-07-14 10:23:17] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info&t=1752481397007
[14-Jul-2025 10:23:17 Europe/Berlin] Request method: GET
[14-Jul-2025 10:23:17 Europe/Berlin] Session ID: none
[14-Jul-2025 10:23:25 Europe/Berlin] 
================================================================================
[14-Jul-2025 10:23:25 Europe/Berlin] [2025-07-14 10:23:25] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info&t=1752481405486
[14-Jul-2025 10:23:25 Europe/Berlin] Request method: GET
[14-Jul-2025 10:23:25 Europe/Berlin] Session ID: none
[14-Jul-2025 10:30:46 Europe/Berlin] 
================================================================================
[14-Jul-2025 10:30:46 Europe/Berlin] [2025-07-14 10:30:46] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info&t=1752481846208
[14-Jul-2025 10:30:46 Europe/Berlin] Request method: GET
[14-Jul-2025 10:30:46 Europe/Berlin] Session ID: none
[14-Jul-2025 10:32:03 Europe/Berlin] 
================================================================================
[14-Jul-2025 10:32:03 Europe/Berlin] [2025-07-14 10:32:03] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info&t=1752481923801
[14-Jul-2025 10:32:03 Europe/Berlin] Request method: GET
[14-Jul-2025 10:32:03 Europe/Berlin] Session ID: none
[14-Jul-2025 11:34:01 Europe/Berlin] 
================================================================================
[14-Jul-2025 11:34:01 Europe/Berlin] [2025-07-14 11:34:01] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info&t=1752485641363
[14-Jul-2025 11:34:01 Europe/Berlin] Request method: GET
[14-Jul-2025 11:34:01 Europe/Berlin] Session ID: none
[15-Jul-2025 00:26:58 Europe/Berlin] 
================================================================================
[15-Jul-2025 00:26:58 Europe/Berlin] [2025-07-15 00:26:58] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info&t=1752532018629
[15-Jul-2025 00:26:58 Europe/Berlin] Request method: GET
[15-Jul-2025 00:26:58 Europe/Berlin] Session ID: none
[15-Jul-2025 02:25:53 Europe/Berlin] 
================================================================================
[15-Jul-2025 02:25:53 Europe/Berlin] [2025-07-15 02:25:53] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info&t=1752539153496
[15-Jul-2025 02:25:53 Europe/Berlin] Request method: GET
[15-Jul-2025 02:25:53 Europe/Berlin] Session ID: none
[15-Jul-2025 10:50:37 Europe/Berlin] 
================================================================================
[15-Jul-2025 10:50:37 Europe/Berlin] [2025-07-15 10:50:37] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_data
[15-Jul-2025 10:50:37 Europe/Berlin] Request method: GET
[15-Jul-2025 10:50:37 Europe/Berlin] Session ID: none
[15-Jul-2025 10:56:41 Europe/Berlin] 
================================================================================
[15-Jul-2025 10:56:41 Europe/Berlin] [2025-07-15 10:56:41] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_data
[15-Jul-2025 10:56:41 Europe/Berlin] Request method: GET
[15-Jul-2025 10:56:41 Europe/Berlin] Session ID: none
