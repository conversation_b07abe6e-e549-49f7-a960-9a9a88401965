<!-- Deposit Modal -->
<div id="deposit-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>💳 Deposit KMS Credit</h2>
        
        <div class="form-group">
            <label>Select Amount:</label>
            <div class="amount-buttons">
                <button class="amount-btn" data-amount="10">$10</button>
                <button class="amount-btn" data-amount="25">$25</button>
                <button class="amount-btn" data-amount="50">$50</button>
                <button class="amount-btn" data-amount="100">$100</button>
                <button class="amount-btn" data-amount="250">$250</button>
                <button class="amount-btn" data-amount="500">$500</button>
                <button class="amount-btn" data-amount="1000">$1000</button>
                <button class="amount-btn" data-amount="2500">$2500</button>
                <button class="amount-btn" data-amount="5000">$5000</button>
                <button class="amount-btn" data-amount="9500">$9500</button>
            </div>
        </div>
        
        <div class="form-group">
            <label for="custom-deposit-amount">Or enter custom amount:</label>
            <input type="number" id="custom-deposit-amount" min="1" max="9500" step="0.01" placeholder="Enter amount">
        </div>
        
        <div class="form-group">
            <label>Payment Method:</label>
            <div class="payment-methods">
                <button type="button" class="payment-method-btn" data-method="paypal">
                    <img src="../../assets/images/payment/paypal.png" alt="PayPal" class="payment-icon">
                    PayPal
                </button>
                <button type="button" class="payment-method-btn" data-method="stripe">
                    <img src="../../assets/images/payment/credit-card.png" alt="Credit Card" class="payment-icon">
                    Credit Card
                </button>
                <button type="button" class="payment-method-btn" data-method="square">
                    <img src="../../assets/images/payment/square.png" alt="Square" class="payment-icon">
                    Square
                </button>
                <button type="button" class="payment-method-btn" data-method="venmo">
                    <img src="../../assets/images/payment/venmo.png" alt="Venmo" class="payment-icon">
                    Venmo
                </button>
                <button type="button" class="payment-method-btn" data-method="zelle">
                    <img src="../../assets/images/payment/zelle.png" alt="Zelle" class="payment-icon">
                    Zelle
                </button>
            </div>
            <input type="hidden" id="deposit-method" value="">
        </div>
        
        <div class="form-group">
            <button class="btn-primary" id="process-deposit">Process Deposit</button>
        </div>
    </div>
</div>

<!-- Withdraw Modal -->
<div id="withdraw-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>💰 Withdraw KMS Credit</h2>
        
        <div class="form-group">
            <label>Available Balance: <span id="withdraw-balance">$0.00</span></label>
        </div>
        
        <div class="form-group">
            <label>Select Amount:</label>
            <div class="amount-buttons">
                <button class="amount-btn" data-amount="100">$100</button>
                <button class="amount-btn" data-amount="250">$250</button>
                <button class="amount-btn" data-amount="500">$500</button>
                <button class="amount-btn" data-amount="1000">$1000</button>
                <button class="amount-btn" data-amount="2500">$2500</button>
                <button class="amount-btn" data-amount="5000">$5000</button>
            </div>
        </div>
        
        <div class="form-group">
            <label for="custom-withdraw-amount">Or enter custom amount:</label>
            <input type="number" id="custom-withdraw-amount" min="100" step="0.01" placeholder="Minimum $100">
        </div>
        
        <div class="form-group">
            <label>Withdrawal Method:</label>
            <div class="payment-methods">
                <button type="button" class="payment-method-btn" data-method="paypal">
                    <img src="../../assets/images/payment/paypal.png" alt="PayPal" class="payment-icon">
                    PayPal
                </button>
                <button type="button" class="payment-method-btn" data-method="venmo">
                    <img src="../../assets/images/payment/venmo.png" alt="Venmo" class="payment-icon">
                    Venmo
                </button>
                <button type="button" class="payment-method-btn" data-method="zelle">
                    <img src="../../assets/images/payment/zelle.png" alt="Zelle" class="payment-icon">
                    Zelle
                </button>
                <button type="button" class="payment-method-btn" data-method="bank_transfer">
                    <img src="../../assets/images/payment/bank.png" alt="Bank Transfer" class="payment-icon">
                    Bank Transfer
                </button>
            </div>
            <input type="hidden" id="withdraw-method" value="">
        </div>
        
        <div class="form-group">
            <label for="withdraw-details">Payment Details:</label>
            <textarea id="withdraw-details" placeholder="Enter your payment account details (email, phone, account number, etc.)"></textarea>
        </div>
        
        <div class="form-group">
            <button class="btn-primary" id="process-withdraw">Request Withdrawal</button>
        </div>
        
        <div class="form-group">
            <small>Note: Withdrawals are processed within 1-3 business days. Minimum withdrawal amount is $100.</small>
        </div>
    </div>
</div>

<!-- Transfer Modal -->
<div id="transfer-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>🔄 Transfer Commission to KMS Credit</h2>
        
        <div class="form-group">
            <label>Available Commission Balance: <span id="commission-balance">$0.00</span></label>
        </div>
        
        <div class="form-group">
            <label for="transfer-amount">Transfer Amount:</label>
            <input type="number" id="transfer-amount" min="1" step="0.01" placeholder="Enter amount to transfer">
        </div>
        
        <div class="form-group">
            <button class="btn-primary" id="process-transfer">Transfer to KMS Credit</button>
        </div>
        
        <div class="form-group">
            <small>Note: Commission balance can be transferred to KMS Credit for use on services and PC builds.</small>
        </div>
    </div>
</div>

<style>
.amount-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 10px;
    margin: 10px 0;
}

.amount-btn {
    padding: 10px;
    border: 2px solid #2196F3;
    background: rgba(33, 150, 243, 0.1);
    color: #2196F3;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.amount-btn:hover,
.amount-btn.selected {
    background: #2196F3;
    color: white;
}

.payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin: 10px 0;
}

.payment-method-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 8px;
    border: 2px solid #00bcaa;
    background: rgba(0, 188, 170, 0.1);
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.payment-method-btn:hover {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.2);
    transform: translateY(-2px);
}

.payment-method-btn.selected {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.3);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.payment-icon {
    width: 32px;
    height: 32px;
    margin-bottom: 5px;
    object-fit: contain;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    overflow-y: auto;
}

.modal-content {
    background-color: #2b9869;
    margin: 5% auto;
    padding: 20px;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    position: relative;
    max-height: 90vh;
    overflow-y: auto;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 10px;
}

.close:hover {
    color: white;
}

.form-group {
    margin: 15px 0;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #00ffff;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background: rgba(255,255,255,0.9);
    color: #333;
    box-sizing: border-box;
}

.form-group textarea {
    background-color: #ffc000;
    resize: vertical;
    min-height: 80px;
}

.btn-primary {
    background: linear-gradient(145deg, #2196F3, #0b7dda);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    width: 100%;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 95vh;
    }
    
    .amount-buttons {
        grid-template-columns: repeat(3, 1fr);
    }
}
</style>

<script>
// Amount button functionality
document.addEventListener('DOMContentLoaded', function() {
    // Handle amount button clicks
    document.querySelectorAll('.amount-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const amount = this.dataset.amount;
            const modal = this.closest('.modal');
            const customInput = modal.querySelector('input[type="number"]');
            
            // Remove selected class from all buttons in this modal
            modal.querySelectorAll('.amount-btn').forEach(b => b.classList.remove('selected'));
            
            // Add selected class to clicked button
            this.classList.add('selected');
            
            // Set the custom input value
            if (customInput) {
                customInput.value = amount;
            }
        });
    });
    
    // Handle custom amount input
    document.querySelectorAll('input[type="number"]').forEach(input => {
        input.addEventListener('input', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.querySelectorAll('.amount-btn').forEach(btn => btn.classList.remove('selected'));
            }
        });
    });
});
</script>
