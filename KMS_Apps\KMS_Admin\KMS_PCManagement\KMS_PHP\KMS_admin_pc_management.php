<?php
// Admin PC Components Management Page
session_start();
$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Language' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_language.php';

// Check if user is admin
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    header('Location: ../index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - PC Management - KelvinKMS.com</title>
    <link rel="stylesheet" href="../../../../KMS_Apps/KMS_Index/KMS_Authentication/KMS_CSS/KMS_custom-modal.css" />
    <style>
        body { font-family: Arial, sans-serif; background-color: #a48f19; color: white; margin: 0; padding: 20px; }

        /* Custom scrollbar for all pages */
        ::-webkit-scrollbar {
            width: 3px;
        }
        ::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
        }
        ::-webkit-scrollbar-thumb {
            background: #05c3b6;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #04a89d;
        }
        .container { max-width: 1400px; margin: auto; background-color: rgb(5 195 182); padding: 10px; border-radius: 10px; box-shadow: 0 2px 8px rgb(0 0 0); }
        h1, h2 { color: #ffffff; text-align: center; font-size: 26px; }

        /* Navigation */
        .nav-controls { text-align: center; margin-bottom: 30px; }
        .nav-controls button {
            padding: 5px 10px;
            margin: 0 5px;
            border: 2px solid rgb(253, 202, 0);
            border-radius: 10px;
            background-color: rgb(253, 202, 0);
            color: #ffffff;
            cursor: pointer;
            font-weight: bold;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
        }
        .nav-controls button:hover {
            background-color: #ffbf00ff;
            border-color: rgba(255, 255, 255, 0.3);
        }
        .nav-controls button.active {
            background-color: #00d2f9;
            border-color: rgba(255, 255, 255, 0.3);
            color: white;
        }

        /* Stats Cards */
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border-left: 5px solid rgb(253, 202, 0);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .stat-card h3 { margin: 0 0 10px 0; color: #ffffff; font-size: 16px; font-weight: bold; }
        .stat-value { font-size: 28px; font-weight: bold; color: #32cd32; }
        .stat-label { font-size: 12px; color: #ffffff; margin-top: 5px; }

        /* Tables */
        .table-container {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .table-container h3 { color: #ffffff; margin-top: 0; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid rgba(255, 255, 255, 0.2); }
        th { background-color: rgba(0, 0, 0, 0.3); color: #ffffff; font-weight: bold; }
        tr:hover { background-color: rgba(255, 255, 255, 0.2); }

        /* Buttons */
        .btn {
            padding: 5px 10px;
            border: 2px solid;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            margin: 2px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
            border-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
            border-color: #17a2b8;
        }
        .btn-info:hover {
            background-color: #138496;
            border-color: #117a8b;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #000;
            border-color: #ffc107;
        }
        .btn-warning:hover {
            background-color: #e0a800;
            border-color: #d39e00;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #004085;
        }

        /* Forms */
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; color: white; }
        .form-group input, .form-group select {
            width: 100%; padding: 8px; border-radius: 5px; border: 1px solid #666;
            background-color: #444; color: white;
        }
        .form-group textarea {
            width: 100%; padding: 8px; border-radius: 5px; border: 1px solid #666;
            background-color: #ffc000; color: black;
        }
        .form-row { display: flex; gap: 20px; }
        .form-row .form-group { flex: 1; }

        /* Back to Admin button */
        .back-admin-btn {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: #00bcaa;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 5px 10px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
            text-decoration: none;
        }
        .back-admin-btn:hover {
            background-color: #ffbf00ff;
            border-color: rgba(255, 255, 255, 0.3);
        }

        /* Search and filters */
        .search-bar { margin-bottom: 20px; }
        .search-bar input {
            width: 300px;
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #666;
            background-color: #444;
            color: white;
        }

        /* Component and config cards */
        .component-card, .prebuilt-card, .category-card {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .component-card { border-left: 5px solid #17a2b8; }
        .prebuilt-card { border-left: 5px solid #ffc107; }
        .category-card { border-left: 5px solid #28a745; }

        .component-specs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .spec-item {
            background: rgba(0,0,0,0.3);
            padding: 8px;
            border-radius: 5px;
            font-size: 12px;
        }
        .price-display {
            font-size: 24px;
            font-weight: bold;
            color: #32cd32;
        }
        .discount-badge {
            background: #dc3545;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 10px;
        }

        /* Hidden sections */
        .section { display: none; }
        .section.active { display: block; }

        /* Modals */
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.8); }
        .modal-content { background-color: rgb(5 195 182); margin: 5% auto; padding: 30px; border-radius: 15px; width: 90%; max-width: 600px; }
        .modal h3 { color: #ffffff; text-align: center; margin-bottom: 20px; }
        .close { position: absolute; right: 15px; top: 15px; font-size: 28px; font-weight: bold; color: #ccc; cursor: pointer; }
        .close:hover { color: #ff4500; }
    </style>
</head>
<body>
    <!-- Back to Admin button -->
    <a href="../../KMS_Dashboard/KMS_PHP/KMS_admin.php" class="back-admin-btn">← Back to Admin Panel</a>

    <div class="container">
        <h3>💻 PC Components Management</h3>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="total-components">0</div>
                <div class="stat-label">Total Components</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="total-prebuilt">0</div>
                <div class="stat-label">Pre-built Configs</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="total-orders">0</div>
                <div class="stat-label">PC Orders</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="pending-orders">0</div>
                <div class="stat-label">Pending Orders</div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="nav-controls">
            <button onclick="switchTab('components')" class="nav-btn active" data-section="components">🔧 Components</button>
            <button onclick="switchTab('prebuilt')" class="nav-btn" data-section="prebuilt">📦 Pre-built Configs</button>
            <button onclick="switchTab('orders')" class="nav-btn" data-section="orders">📋 PC Orders</button>
            <button onclick="switchTab('categories')" class="nav-btn" data-section="categories">📂 Categories</button>
        </div>

        <!-- Components Section -->
        <div id="components" class="section active">
            <div class="table-container">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>PC Components</h3>
                    <button class="btn btn-success" onclick="openAddComponentModal()">➕ Add Component</button>
                </div>

                <div class="search-bar">
                    <input type="text" id="component-search" placeholder="Search components..." onkeyup="searchComponents()">
                    <select id="category-filter" onchange="filterComponents()" style="margin-left: 10px; padding: 8px; background: #444; color: white; border: 1px solid #666;">
                        <option value="">All Categories</option>
                    </select>
                </div>

                <div id="components-list">
                    <div style="text-align: center; padding: 40px; color: #ccc;">
                        Loading components...
                    </div>
                </div>
            </div>
        </div>

        <!-- Pre-built Configs Section -->
        <div id="prebuilt" class="section">
            <div class="table-container">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>Pre-built Configurations</h3>
                    <button class="btn btn-success" onclick="openAddPrebuiltModal()">➕ Add Pre-built Config</button>
                </div>

                <div class="search-bar">
                    <input type="text" id="prebuilt-search" placeholder="Search configurations..." onkeyup="searchPrebuilt()">
                    <select id="tier-filter" onchange="filterPrebuilt()" style="margin-left: 10px; padding: 8px; background: #444; color: white; border: 1px solid #666;">
                        <option value="">All Tiers</option>
                        <option value="entry">Entry</option>
                        <option value="mid">Mid-Range</option>
                        <option value="high">High-End</option>
                        <option value="extreme">Extreme</option>
                    </select>
                </div>

                <div id="prebuilt-list">
                    <div style="text-align: center; padding: 40px; color: #ccc;">
                        Loading pre-built configurations...
                    </div>
                </div>
            </div>
        </div>

        <!-- PC Orders Section -->
        <div id="orders" class="section">
            <div class="table-container">
                <h3>PC Orders Management</h3>

                <div class="search-bar">
                    <input type="text" id="orders-search" placeholder="Search orders..." onkeyup="searchOrders()">
                    <select id="status-filter" onchange="filterOrders()" style="margin-left: 10px; padding: 8px; background: #444; color: white; border: 1px solid #666;">
                        <option value="">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="quoted">Quoted</option>
                        <option value="confirmed">Confirmed</option>
                        <option value="processing">Processing</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>

                <div id="orders-list">
                    <div style="text-align: center; padding: 40px; color: #ccc;">
                        Loading PC orders...
                    </div>
                </div>
            </div>
        </div>

        <!-- Categories Section -->
        <div id="categories" class="section">
            <div class="table-container">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>Component Categories</h3>
                    <button class="btn btn-success" onclick="openAddCategoryModal()">➕ Add Category</button>
                </div>

                <div id="categories-list">
                    <div style="text-align: center; padding: 40px; color: #ccc;">
                        Loading categories...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Component Modal -->
    <div id="addComponentModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('addComponentModal')">&times;</span>
            <h3>Add New Component</h3>
            <form id="addComponentForm">
                <div class="form-row">
                    <div class="form-group">
                        <label>Category</label>
                        <select id="add-category" required>
                            <option value="">Select Category</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Brand</label>
                        <input type="text" id="add-brand" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Component Name</label>
                        <input type="text" id="add-component-name" required>
                    </div>
                    <div class="form-group">
                        <label>Model</label>
                        <input type="text" id="add-model">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Base Price ($)</label>
                        <input type="number" id="add-base-price" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>Current Price ($)</label>
                        <input type="number" id="add-current-price" step="0.01" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Stock Quantity</label>
                        <input type="number" id="add-stock" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>Sort Order</label>
                        <input type="number" id="add-sort-order" value="0">
                    </div>
                </div>

                <div class="form-group">
                    <label>Description</label>
                    <textarea id="add-description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label><input type="checkbox" id="add-is-active" checked> Active</label>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button type="submit" class="btn btn-success">Add Component</button>
                    <button type="button" class="btn btn-danger" onclick="closeModal('addComponentModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Component Modal -->
    <div id="editComponentModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('editComponentModal')">&times;</span>
            <h3>Edit Component</h3>
            <form id="editComponentForm">
                <input type="hidden" id="edit-component-id">
                <div class="form-row">
                    <div class="form-group">
                        <label>Category</label>
                        <select id="edit-category" required>
                            <option value="">Select Category</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Brand</label>
                        <input type="text" id="edit-brand" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Component Name</label>
                        <input type="text" id="edit-component-name" required>
                    </div>
                    <div class="form-group">
                        <label>Model</label>
                        <input type="text" id="edit-model">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Base Price ($)</label>
                        <input type="number" id="edit-base-price" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>Current Price ($)</label>
                        <input type="number" id="edit-current-price" step="0.01" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Stock Quantity</label>
                        <input type="number" id="edit-stock" min="0">
                    </div>
                    <div class="form-group">
                        <label>Sort Order</label>
                        <input type="number" id="edit-sort-order">
                    </div>
                </div>

                <div class="form-group">
                    <label>Description</label>
                    <textarea id="edit-description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label><input type="checkbox" id="edit-is-active"> Active</label>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button type="submit" class="btn btn-success">Update Component</button>
                    <button type="button" class="btn btn-danger" onclick="closeModal('editComponentModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Pre-built Config Modal -->
    <div id="addPrebuiltModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('addPrebuiltModal')">&times;</span>
            <h3>Add New Pre-built Configuration</h3>
            <form id="addPrebuiltForm">
                <div class="form-row">
                    <div class="form-group">
                        <label>Configuration Name</label>
                        <input type="text" id="add-prebuilt-name" required>
                    </div>
                    <div class="form-group">
                        <label>Tier</label>
                        <select id="add-prebuilt-tier" required>
                            <option value="">Select Tier</option>
                            <option value="entry">Entry</option>
                            <option value="mid">Mid-Range</option>
                            <option value="high">High-End</option>
                            <option value="extreme">Extreme</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Primary Use</label>
                        <select id="add-prebuilt-use" required>
                            <option value="">Select Use</option>
                            <option value="gaming">Gaming</option>
                            <option value="video_editing">Video Editing</option>
                            <option value="both">Both</option>
                            <option value="general">General</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Discount %</label>
                        <input type="number" id="add-prebuilt-discount" step="0.01" min="0" max="100" value="0">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Base Price ($)</label>
                        <input type="number" id="add-prebuilt-base-price" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>Current Price ($)</label>
                        <input type="number" id="add-prebuilt-current-price" step="0.01" required>
                    </div>
                </div>
                <div class="form-group">
                    <label>Description</label>
                    <textarea id="add-prebuilt-description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label>Components (JSON format)</label>
                    <textarea id="add-prebuilt-components" rows="6" placeholder='{"cpu": {"name": "Intel i7", "price": 400}, "gpu": {"name": "RTX 4070", "price": 600}}'></textarea>
                </div>
                <div class="form-group">
                    <label>Specifications Summary (JSON format)</label>
                    <textarea id="add-prebuilt-specs" rows="4" placeholder='{"cpu": "Intel i7", "gpu": "RTX 4070", "ram": "32GB", "storage": "1TB SSD"}'></textarea>
                </div>
                <div class="form-group">
                    <label><input type="checkbox" id="add-prebuilt-active" checked> Active</label>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button type="submit" class="btn btn-success">Add Configuration</button>
                    <button type="button" class="btn btn-danger" onclick="closeModal('addPrebuiltModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Pre-built Config Modal -->
    <div id="editPrebuiltModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('editPrebuiltModal')">&times;</span>
            <h3>Edit Pre-built Configuration</h3>
            <form id="editPrebuiltForm">
                <input type="hidden" id="edit-prebuilt-id">
                <div class="form-row">
                    <div class="form-group">
                        <label>Configuration Name</label>
                        <input type="text" id="edit-prebuilt-name" required>
                    </div>
                    <div class="form-group">
                        <label>Tier</label>
                        <select id="edit-prebuilt-tier" required>
                            <option value="">Select Tier</option>
                            <option value="entry">Entry</option>
                            <option value="mid">Mid-Range</option>
                            <option value="high">High-End</option>
                            <option value="extreme">Extreme</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Primary Use</label>
                        <select id="edit-prebuilt-use" required>
                            <option value="">Select Use</option>
                            <option value="gaming">Gaming</option>
                            <option value="video_editing">Video Editing</option>
                            <option value="both">Both</option>
                            <option value="general">General</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Discount %</label>
                        <input type="number" id="edit-prebuilt-discount" step="0.01" min="0" max="100">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Base Price ($)</label>
                        <input type="number" id="edit-prebuilt-base-price" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>Current Price ($)</label>
                        <input type="number" id="edit-prebuilt-current-price" step="0.01" required>
                    </div>
                </div>
                <div class="form-group">
                    <label>Description</label>
                    <textarea id="edit-prebuilt-description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label>Components (JSON format)</label>
                    <textarea id="edit-prebuilt-components" rows="6"></textarea>
                </div>
                <div class="form-group">
                    <label>Specifications Summary (JSON format)</label>
                    <textarea id="edit-prebuilt-specs" rows="4"></textarea>
                </div>
                <div class="form-group">
                    <label><input type="checkbox" id="edit-prebuilt-active"> Active</label>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button type="submit" class="btn btn-success">Update Configuration</button>
                    <button type="button" class="btn btn-danger" onclick="closeModal('editPrebuiltModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Global variables
        let components = [];
        let prebuiltConfigs = [];
        let pcOrders = [];
        let categories = [];
        let currentTab = 'components';

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadCategories();
            loadComponents();
            loadPrebuiltConfigs();
            loadPCOrders();
        });

        // Tab switching
        function switchTab(tabName) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected section
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            currentTab = tabName;
        }

        // Load statistics
        function loadStatistics() {
            // This would typically make API calls to get real statistics
            // For now, we'll update them as we load data
        }

        // Load categories
        function loadCategories() {
            fetch('../../../KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php?action=get_categories', {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    categories = data.categories;
                    populateCategoryFilter();
                    displayCategories();
                }
            })
            .catch(error => console.error('Error loading categories:', error));
        }

        function populateCategoryFilter() {
            const filter = document.getElementById('category-filter');
            filter.innerHTML = '<option value="">All Categories</option>';
            categories.forEach(category => {
                filter.innerHTML += `<option value="${category.id}">${category.category_name}</option>`;
            });
        }

        function displayCategories() {
            const container = document.getElementById('categories-list');
            if (categories.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #ccc;">No categories found.</div>';
                return;
            }

            container.innerHTML = categories.map(category => `
                <div class="category-card">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h4 style="margin: 0; color: #ffffff;">${category.category_name}</h4>
                            <p style="margin: 5px 0; color: #ccc;">${category.description || 'No description'}</p>
                            <div style="font-size: 12px; color: #888;">
                                Sort Order: ${category.sort_order} |
                                Status: ${category.is_active ? '<span style="color: #32cd32;">Active</span>' : '<span style="color: #dc3545;">Inactive</span>'}
                            </div>
                        </div>
                        <div>
                            <button class="btn btn-warning" onclick="editCategory(${category.id})">Edit</button>
                            <button class="btn btn-danger" onclick="deleteCategory(${category.id})">Delete</button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Load components
        function loadComponents() {
            fetch('../../../KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php?action=get_components&active_only=0', {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    components = data.components;
                    displayComponents();
                    updateStatistics();
                }
            })
            .catch(error => console.error('Error loading components:', error));
        }

        function displayComponents() {
            const container = document.getElementById('components-list');
            if (components.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #ccc;">No components found.</div>';
                return;
            }

            container.innerHTML = components.map(component => `
                <div class="component-card">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                        <div style="flex: 1;">
                            <h3 style="margin: 0; color: #00ffff;">${component.component_name}</h3>
                            <div style="margin: 5px 0; color: white;">
                                <strong>Brand:</strong> ${component.brand || 'N/A'} |
                                <strong>Model:</strong> ${component.model || 'N/A'} |
                                <strong>Category:</strong> ${component.category_name}
                            </div>
                            <div style="margin: 10px 0; color: white;">
                                <strong>Description:</strong> ${component.description || 'No description'}
                            </div>
                            <div style="margin-top: 10px;">
                                <span class="price-display">$${parseFloat(component.current_price).toFixed(2)}</span>
                                ${component.base_price !== component.current_price ?
                                    `<span style="text-decoration: line-through; color: #ccc; margin-left: 10px;">$${parseFloat(component.base_price).toFixed(2)}</span>` : ''
                                }
                                <span style="margin-left: 15px; color: #888;">Stock: ${component.stock_quantity}</span>
                                ${!component.is_active ? '<span style="margin-left: 15px; color: #ff6b6b;">INACTIVE</span>' : ''}
                            </div>
                        </div>
                        <div style="margin-left: 20px;">
                            <button class="btn btn-warning" onclick="editComponent(${component.id})">Edit</button>
                            <button class="btn btn-danger" onclick="deleteComponent(${component.id})">Delete</button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Load prebuilt configs
        function loadPrebuiltConfigs() {
            fetch('../../../KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php?action=get_prebuilt_configs&active_only=0', {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    prebuiltConfigs = data.configs;
                    displayPrebuiltConfigs();
                    updateStatistics();
                }
            })
            .catch(error => console.error('Error loading prebuilt configs:', error));
        }

        function displayPrebuiltConfigs() {
            const container = document.getElementById('prebuilt-list');
            if (prebuiltConfigs.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #ccc;">No pre-built configurations found.</div>';
                return;
            }

            container.innerHTML = prebuiltConfigs.map(config => `
                <div class="prebuilt-card">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                        <div style="flex: 1;">
                            <h3 style="margin: 0; color: #ffd700;">${config.config_name}</h3>
                            <div style="margin: 5px 0; color: #ccc;">
                                <strong>Tier:</strong> ${config.tier.charAt(0).toUpperCase() + config.tier.slice(1)} |
                                <strong>Use:</strong> ${config.primary_use.replace('_', ' ')} |
                                ${!config.is_active ? '<span style="color: #ff6b6b;">INACTIVE</span>' : '<span style="color: #00ff00;">ACTIVE</span>'}
                            </div>
                            <div class="component-specs">
                                ${config.specifications_summary ? Object.entries(config.specifications_summary).map(([key, value]) =>
                                    `<div class="spec-item"><strong>${key}:</strong> ${value}</div>`
                                ).join('') : '<div class="spec-item">No specifications summary</div>'}
                            </div>
                            <div style="margin-top: 10px;">
                                <span class="price-display">$${parseFloat(config.current_price || 0).toFixed(2)}</span>
                                ${parseFloat(config.discount_percentage || 0) > 0 ?
                                    `<span class="discount-badge">${parseFloat(config.discount_percentage || 0).toFixed(1)}% OFF</span>` : ''
                                }
                                ${parseFloat(config.base_price || 0) !== parseFloat(config.current_price || 0) ?
                                    `<span style="text-decoration: line-through; color: #ccc; margin-left: 10px;">$${parseFloat(config.base_price || 0).toFixed(2)}</span>` : ''
                                }
                            </div>
                            <div style="margin-top: 10px; font-size: 12px; color: #ccc;">
                                ${config.description || 'No description'}
                            </div>
                        </div>
                        <div style="margin-left: 20px;">
                            <button class="btn btn-warning" onclick="editPrebuiltConfig(${config.id})">Edit</button>
                            <button class="btn btn-danger" onclick="deletePrebuiltConfig(${config.id})">Delete</button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Load PC orders
        function loadPCOrders() {
            fetch('../../../KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php?action=admin_get_pc_orders', {
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    pcOrders = data.orders;
                    displayPCOrders();
                    updateStatistics();
                }
            })
            .catch(error => console.error('Error loading PC orders:', error));
        }

        function displayPCOrders() {
            const container = document.getElementById('orders-list');
            if (pcOrders.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #ccc;">No PC orders found.</div>';
                return;
            }

            container.innerHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Customer</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Price</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${pcOrders.map(order => `
                            <tr>
                                <td>${order.order_number}</td>
                                <td>${order.username}<br><small style="color: #ccc;">${order.email}</small></td>
                                <td>${order.order_type.charAt(0).toUpperCase() + order.order_type.slice(1)}</td>
                                <td>
                                    <span style="color: ${getStatusColor(order.status)};">
                                        ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                    </span>
                                </td>
                                <td>
                                    ${order.final_price ? '$' + parseFloat(order.final_price).toFixed(2) :
                                      order.estimated_price ? '$' + parseFloat(order.estimated_price).toFixed(2) + ' (est.)' : 'TBD'}
                                </td>
                                <td>${new Date(order.created_at).toLocaleDateString()}</td>
                                <td>
                                    <button class="btn btn-primary" onclick="viewPCOrder(${order.id})">View</button>
                                    <button class="btn btn-warning" onclick="editPCOrder(${order.id})">Edit</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }

        function getStatusColor(status) {
            const colors = {
                'pending': '#ffc107',
                'quoted': '#17a2b8',
                'confirmed': '#007bff',
                'processing': '#fd7e14',
                'completed': '#28a745',
                'cancelled': '#dc3545',
                'refunded': '#6c757d'
            };
            return colors[status] || '#ccc';
        }

        function updateStatistics() {
            document.getElementById('total-components').textContent = components.length;
            document.getElementById('total-prebuilt').textContent = prebuiltConfigs.length;
            document.getElementById('total-orders').textContent = pcOrders.length;
            document.getElementById('pending-orders').textContent = pcOrders.filter(order => order.status === 'pending').length;
        }

        // Search and filter functions
        function searchComponents() {
            const searchTerm = document.getElementById('component-search').value.toLowerCase();
            const filteredComponents = components.filter(component =>
                component.component_name.toLowerCase().includes(searchTerm) ||
                component.brand.toLowerCase().includes(searchTerm) ||
                component.model.toLowerCase().includes(searchTerm)
            );
            displayFilteredComponents(filteredComponents);
        }

        function filterComponents() {
            const categoryId = document.getElementById('category-filter').value;
            const filteredComponents = categoryId ?
                components.filter(component => component.category_id == categoryId) :
                components;
            displayFilteredComponents(filteredComponents);
        }

        function displayFilteredComponents(filteredComponents) {
            // Use the same display logic but with filtered data
            const originalComponents = components;
            components = filteredComponents;
            displayComponents();
            components = originalComponents;
        }

        // Modal functions
        function openAddComponentModal() {
            populateModalCategories('add-category');
            document.getElementById('addComponentModal').style.display = 'block';
        }

        function editComponent(id) {
            const component = components.find(c => c.id === id);
            if (!component) return;

            populateModalCategories('edit-category');

            document.getElementById('edit-component-id').value = component.id;
            document.getElementById('edit-category').value = component.category_id;
            document.getElementById('edit-brand').value = component.brand || '';
            document.getElementById('edit-component-name').value = component.component_name;
            document.getElementById('edit-model').value = component.model || '';
            document.getElementById('edit-base-price').value = component.base_price;
            document.getElementById('edit-current-price').value = component.current_price;
            document.getElementById('edit-stock').value = component.stock_quantity;
            document.getElementById('edit-sort-order').value = component.sort_order;

            document.getElementById('edit-description').value = component.description || '';
            document.getElementById('edit-is-active').checked = component.is_active == 1;

            document.getElementById('editComponentModal').style.display = 'block';
        }

        function deleteComponent(id) {
            if (confirm('Are you sure you want to delete this component?')) {
                const formData = new FormData();
                formData.append('action', 'delete_component');
                formData.append('id', id);

                fetch('../../../KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php', {
                    method: 'POST',
                    credentials: 'same-origin',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Component deleted successfully!');
                        loadComponents();
                    } else {
                        alert('Failed to delete component: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error deleting component:', error);
                    alert('Error deleting component.');
                });
            }
        }

        function populateModalCategories(selectId) {
            const select = document.getElementById(selectId);
            select.innerHTML = '<option value="">Select Category</option>';
            categories.forEach(category => {
                select.innerHTML += `<option value="${category.id}">${category.category_name}</option>`;
            });
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Form submissions
        document.getElementById('addComponentForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'add_component');
            formData.append('category_id', document.getElementById('add-category').value);
            formData.append('brand', document.getElementById('add-brand').value);
            formData.append('component_name', document.getElementById('add-component-name').value);
            formData.append('component_name_en', document.getElementById('add-component-name').value);
            formData.append('component_name_zh', document.getElementById('add-component-name').value);
            formData.append('model', document.getElementById('add-model').value);
            formData.append('base_price', document.getElementById('add-base-price').value);
            formData.append('current_price', document.getElementById('add-current-price').value);
            formData.append('stock_quantity', document.getElementById('add-stock').value);
            formData.append('sort_order', document.getElementById('add-sort-order').value);
            formData.append('specifications', '{}');
            formData.append('description', document.getElementById('add-description').value);
            formData.append('description_en', document.getElementById('add-description').value);
            formData.append('description_zh', document.getElementById('add-description').value);
            if (document.getElementById('add-is-active').checked) {
                formData.append('is_active', '1');
            }

            fetch('../../../KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php', {
                method: 'POST',
                credentials: 'same-origin',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Component added successfully!');
                    closeModal('addComponentModal');
                    document.getElementById('addComponentForm').reset();
                    loadComponents();
                } else {
                    alert('Failed to add component: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error adding component:', error);
                alert('Error adding component.');
            });
        });

        document.getElementById('editComponentForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'update_component');
            formData.append('id', document.getElementById('edit-component-id').value);
            formData.append('category_id', document.getElementById('edit-category').value);
            formData.append('brand', document.getElementById('edit-brand').value);
            formData.append('component_name', document.getElementById('edit-component-name').value);
            formData.append('component_name_en', document.getElementById('edit-component-name').value);
            formData.append('component_name_zh', document.getElementById('edit-component-name').value);
            formData.append('model', document.getElementById('edit-model').value);
            formData.append('base_price', document.getElementById('edit-base-price').value);
            formData.append('current_price', document.getElementById('edit-current-price').value);
            formData.append('stock_quantity', document.getElementById('edit-stock').value);
            formData.append('sort_order', document.getElementById('edit-sort-order').value);
            formData.append('specifications', '{}');
            formData.append('description', document.getElementById('edit-description').value);
            formData.append('description_en', document.getElementById('edit-description').value);
            formData.append('description_zh', document.getElementById('edit-description').value);
            if (document.getElementById('edit-is-active').checked) {
                formData.append('is_active', '1');
            }

            fetch('../../../KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php', {
                method: 'POST',
                credentials: 'same-origin',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Component updated successfully!');
                    closeModal('editComponentModal');
                    loadComponents();
                } else {
                    alert('Failed to update component: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error updating component:', error);
                alert('Error updating component.');
            });
        });

        function openAddPrebuiltModal() {
            document.getElementById('addPrebuiltModal').style.display = 'block';
        }

        function editPrebuiltConfig(id) {
            const config = prebuiltConfigs.find(c => c.id === id);
            if (!config) return;

            document.getElementById('edit-prebuilt-id').value = config.id;
            document.getElementById('edit-prebuilt-name').value = config.config_name;
            document.getElementById('edit-prebuilt-tier').value = config.tier;
            document.getElementById('edit-prebuilt-use').value = config.primary_use;
            document.getElementById('edit-prebuilt-base-price').value = config.base_price;
            document.getElementById('edit-prebuilt-current-price').value = config.current_price;
            document.getElementById('edit-prebuilt-discount').value = config.discount_percentage;
            document.getElementById('edit-prebuilt-description').value = config.description || '';
            document.getElementById('edit-prebuilt-components').value = config.components ? JSON.stringify(config.components, null, 2) : '';
            document.getElementById('edit-prebuilt-specs').value = config.specifications_summary ? JSON.stringify(config.specifications_summary, null, 2) : '';
            document.getElementById('edit-prebuilt-active').checked = config.is_active == 1;

            document.getElementById('editPrebuiltModal').style.display = 'block';
        }

        function deletePrebuiltConfig(id) {
            if (confirm('Are you sure you want to delete this pre-built configuration?')) {
                const formData = new FormData();
                formData.append('action', 'delete_prebuilt_config');
                formData.append('id', id);

                fetch('../../../KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php', {
                    method: 'POST',
                    credentials: 'same-origin',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Pre-built configuration deleted successfully!');
                        loadPrebuiltConfigs();
                    } else {
                        alert('Failed to delete configuration: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error deleting configuration:', error);
                    alert('Error deleting configuration.');
                });
            }
        }

        // Pre-built form submissions
        document.getElementById('addPrebuiltForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'add_prebuilt_config');
            formData.append('config_name', document.getElementById('add-prebuilt-name').value);
            formData.append('config_name_en', document.getElementById('add-prebuilt-name').value);
            formData.append('config_name_zh', document.getElementById('add-prebuilt-name').value);
            formData.append('tier', document.getElementById('add-prebuilt-tier').value);
            formData.append('primary_use', document.getElementById('add-prebuilt-use').value);
            formData.append('base_price', document.getElementById('add-prebuilt-base-price').value);
            formData.append('current_price', document.getElementById('add-prebuilt-current-price').value);
            formData.append('discount_percentage', document.getElementById('add-prebuilt-discount').value);
            formData.append('description', document.getElementById('add-prebuilt-description').value);
            formData.append('description_en', document.getElementById('add-prebuilt-description').value);
            formData.append('description_zh', document.getElementById('add-prebuilt-description').value);
            formData.append('components', document.getElementById('add-prebuilt-components').value || '{}');
            formData.append('specifications_summary', document.getElementById('add-prebuilt-specs').value || '{}');
            if (document.getElementById('add-prebuilt-active').checked) {
                formData.append('is_active', '1');
            }

            fetch('../../../KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php', {
                method: 'POST',
                credentials: 'same-origin',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Pre-built configuration added successfully!');
                    closeModal('addPrebuiltModal');
                    document.getElementById('addPrebuiltForm').reset();
                    loadPrebuiltConfigs();
                } else {
                    alert('Failed to add configuration: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error adding configuration:', error);
                alert('Error adding configuration.');
            });
        });

        document.getElementById('editPrebuiltForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'update_prebuilt_config');
            formData.append('id', document.getElementById('edit-prebuilt-id').value);
            formData.append('config_name', document.getElementById('edit-prebuilt-name').value);
            formData.append('config_name_en', document.getElementById('edit-prebuilt-name').value);
            formData.append('config_name_zh', document.getElementById('edit-prebuilt-name').value);
            formData.append('tier', document.getElementById('edit-prebuilt-tier').value);
            formData.append('primary_use', document.getElementById('edit-prebuilt-use').value);
            formData.append('base_price', document.getElementById('edit-prebuilt-base-price').value);
            formData.append('current_price', document.getElementById('edit-prebuilt-current-price').value);
            formData.append('discount_percentage', document.getElementById('edit-prebuilt-discount').value);
            formData.append('description', document.getElementById('edit-prebuilt-description').value);
            formData.append('description_en', document.getElementById('edit-prebuilt-description').value);
            formData.append('description_zh', document.getElementById('edit-prebuilt-description').value);
            formData.append('components', document.getElementById('edit-prebuilt-components').value || '{}');
            formData.append('specifications_summary', document.getElementById('edit-prebuilt-specs').value || '{}');
            if (document.getElementById('edit-prebuilt-active').checked) {
                formData.append('is_active', '1');
            }

            fetch('../../../KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php', {
                method: 'POST',
                credentials: 'same-origin',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Pre-built configuration updated successfully!');
                    closeModal('editPrebuiltModal');
                    loadPrebuiltConfigs();
                } else {
                    alert('Failed to update configuration: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error updating configuration:', error);
                alert('Error updating configuration.');
            });
        });

        function viewPCOrder(id) {
            const order = pcOrders.find(o => o.id === id);
            if (!order) return;

            let orderDetails = `Order #${order.order_number}\n`;
            orderDetails += `Customer: ${order.username} (${order.email})\n`;
            orderDetails += `Type: ${order.order_type}\n`;
            orderDetails += `Status: ${order.status}\n`;
            orderDetails += `Created: ${new Date(order.created_at).toLocaleString()}\n\n`;

            if (order.configuration) {
                orderDetails += `Configuration:\n${JSON.stringify(order.configuration, null, 2)}\n\n`;
            }

            if (order.notes) {
                orderDetails += `Notes: ${order.notes}\n`;
            }

            alert(orderDetails);
        }

        function editPCOrder(id) {
            const order = pcOrders.find(o => o.id === id);
            if (!order) return;

            const newStatus = prompt('Enter new status (pending, quoted, confirmed, processing, completed, cancelled):', order.status);
            if (newStatus && newStatus !== order.status) {
                const formData = new FormData();
                formData.append('action', 'admin_update_pc_order');
                formData.append('id', id);
                formData.append('status', newStatus);

                fetch('../../../KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php', {
                    method: 'POST',
                    credentials: 'same-origin',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Order status updated successfully!');
                        loadPCOrders();
                    } else {
                        alert('Failed to update order: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error updating order:', error);
                    alert('Error updating order.');
                });
            }
        }

        function openAddCategoryModal() {
            alert('Add Category modal - To be implemented');
        }

        function editCategory(id) {
            alert('Edit Category ' + id + ' - To be implemented');
        }

        function deleteCategory(id) {
            if (confirm('Are you sure you want to delete this category?')) {
                alert('Delete Category ' + id + ' - To be implemented');
            }
        }
    </script>
</body>
</html>
